{% extends "template.html" %}
{% block content %}
    <div class="container">
        <div class="row">
            {% if keywords %}
            <div class="col-md-12 mb-4">
                <div class="card rounded-lg">
                    <div class="card-body">
                        <h5 class="card-title">Keywords</h5>
                        <p class="card-text">{{ keywords }}</p>
                    </div>
                </div>
            </div>
            {% endif %}
            <div class="col-md-12 mb-4">
                <div class="card rounded-lg">
                    <div class="card-body">
                        <div class="card-text">
                            {% for app in unique_apps %}
                                <a href="{{ url_for('search', q=q, app=app) }}"><span class="badge badge-pill badge-primary">{{ app }}</span></a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            <div id="pagination-container" class="row"></div>
            <nav aria-label="Page navigation" style="margin:auto;width:50%">
                <ul id="pagination" class="pagination"></ul>
                <span id="total-items" class="badge badge-pill badge-primary"></span>
                <span id="total-pages" class="badge badge-pill badge-primary"></span>
            </nav>
        </div>
    </div>

    <!-- 存储搜索结果数据 -->
    <script type="application/json" id="search-entries-data">
    {{ entries|default([])|tojson|safe }}
    </script>
{% endblock %}

{% block page_scripts %}
<script src="{{ url_for('static', filename='scripts/search.js') }}"></script>
{% endblock %}
