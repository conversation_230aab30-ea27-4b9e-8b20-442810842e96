<!DOCTYPE html>
<html lang="{{ get_locale() }}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="description" content="{{ _('app_name') }} - {{ _('app_description') }}">
  <meta name="theme-color" content="#4CAF50">
  <title>{{ _('app_name') }}</title>
  <!-- icon -->
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
  <!-- Bootstrap CSS -->
  <link href="{{ url_for('static', filename='bootstrap.min.css') }}" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap-icons.css') }}">
  <!-- 响应式设计CSS -->
  <link rel="stylesheet" href="{{ url_for('static', filename='responsive.css') }}">
  <!-- 主样式表 -->
  <link rel="stylesheet" href="{{ url_for('static', filename='styles/main.css') }}">
  <!-- 预加载关键资源 -->
  <link rel="preload" href="{{ url_for('static', filename='bootstrap.min.js') }}" as="script">
  <link rel="preload" href="{{ url_for('static', filename='jquery.min.js') }}" as="script">

  <!-- 关键主题样式内联，完全避免闪烁 -->
  <style>
    /* 防止闪烁的关键样式 */
    html, body {
      transition: none !important;
      background-color: #ffffff;
      color: #212529;
    }
    html.dark-mode, html.dark-mode body, body.dark-mode {
      background-color: #121212 !important;
      color: #f8f9fa !important;
    }
    /* 隐藏内容直到完全加载 */
    .content-wrapper {
      opacity: 0;
      transition: opacity 0.2s ease-in-out;
    }
    .content-loaded .content-wrapper {
      opacity: 1;
    }
  </style>

  <!-- 页面渲染前应用主题，避免闪烁 -->
  <script>
    (function() {
      // 阻止首次绘制，等待样式完全应用
      document.documentElement.style.display = 'none';

      // 获取存储的主题
      let theme;
      try {
        theme = localStorage.getItem('memococo-theme');
      } catch (e) {
        theme = 'light';
      }

      // 如果是系统主题，检查系统设置
      if (theme === 'system') {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (prefersDark) {
          document.documentElement.classList.add('dark-mode');
        }
      }
      // 如果是暗色主题，直接应用
      else if (theme === 'dark') {
        document.documentElement.classList.add('dark-mode');
      }

      // 允许渲染，但确保样式已应用
      setTimeout(function() {
        document.documentElement.style.display = '';
      }, 0);
    })();
  </script>
</head>
<body data-text-copied="{{ _('text_copied') }}" data-network-error="{{ _('error_network') }}" data-fetch-error="{{ _('error_fetch_data') }}">
{% from 'components/language_switcher.html' import language_switcher %}

<div class="content-wrapper">

<nav class="navbar navbar-light bg-light">
  <div class="container">
    <form class="form-inline my-2 my-lg-0 w-100 d-flex justify-content-center" action="/search" method="get">
      <input class="form-control flex-grow-1 mr-sm-2 rounded-lg" type="search" name="q" placeholder="{{ _('search_placeholder') }}" aria-label="{{ _('nav_search') }}" value="{{ q }}">
      <button class="btn btn-outline-primary my-2 my-sm-0 rounded-lg" type="submit" title="{{ _('nav_search') }}">
        <i class="bi bi-search"></i>
      </button>
    </form>
  </div>
  <div class="d-flex align-items-center">
    {{ language_switcher(get_locale(), get_available_locales()) }}
    <a class="btn btn-outline-primary my-2 my-sm-0 rounded-lg ml-2" href="{{ url_for('settings') }}" title="{{ _('nav_settings') }}">
      <i class="bi bi-gear"></i>
    </a>
  </div>
</nav>
{% block content %}

{% endblock %}

  <!-- Bootstrap and jQuery JS -->
  <script src="{{ url_for('static', filename='jquery.min.js') }}"></script>
  <script src="{{ url_for('static', filename='popper.min.js') }}"></script>
  <script src="{{ url_for('static', filename='bootstrap.min.js') }}"></script>

  <!-- 工具函数和通用功能 -->
  <script src="{{ url_for('static', filename='scripts/utils.js') }}"></script>

  <!-- 响应式设计JS -->
  <script src="{{ url_for('static', filename='lazy-load.js') }}"></script>
  <script src="{{ url_for('static', filename='theme-switcher.js') }}"></script>

  <!-- 页面特定脚本 -->
  {% block page_scripts %}{% endblock %}

  <!-- 主题切换按钮 -->
  <div class="theme-switcher-container">
    <button id="theme-toggle" class="btn btn-sm btn-outline-secondary rounded-circle">
      <i class="bi bi-sun"></i>
    </button>
  </div>

  <script>
    // 初始化主题切换按钮
    document.addEventListener('DOMContentLoaded', function() {
      const themeToggle = document.getElementById('theme-toggle');
      const themeIcon = themeToggle.querySelector('i');

      // 根据当前主题更新图标
      function updateThemeIcon() {
        const theme = themeSwitcher.getEffectiveTheme();
        if (theme === 'dark') {
          themeIcon.className = 'bi bi-moon';
        } else {
          themeIcon.className = 'bi bi-sun';
        }
      }

      // 初始更新图标
      updateThemeIcon();

      // 点击切换主题
      themeToggle.addEventListener('click', function() {
        themeSwitcher.toggleTheme();
        updateThemeIcon();
      });

      // 监听主题变更事件
      window.addEventListener('themechange', updateThemeIcon);

      // 将所有图片转换为懒加载格式
      const images = document.querySelectorAll('img:not(.no-lazy)');
      images.forEach(img => {
        if (!img.classList.contains('lazy-load')) {
          const src = img.src;
          img.setAttribute('data-src', src);
          img.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E';
          img.classList.add('lazy-load');
        }
      });

      // 初始化懒加载
      if (window.lazyLoader) {
        window.lazyLoader.refresh();
      }

      // 标记内容已加载，显示内容
      document.body.classList.add('content-loaded');
    });
  </script>
</div> <!-- .content-wrapper -->

<script>
  // 确保内容在页面加载后显示
  window.addEventListener('load', function() {
    document.body.classList.add('content-loaded');
  });
</script>
</body>
</html>