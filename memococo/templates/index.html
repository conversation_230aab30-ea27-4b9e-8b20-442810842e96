{% extends "template.html" %}
{% block content %}
{% with messages = get_flashed_messages() %}
  {% if messages %}
    <div class="modal fade" id="flashModal" tabindex="-1" role="dialog" aria-labelledby="flashModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="flashModalLabel">{{ _('modal_title') }}</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <ul>
              {% for message in messages %}
                <li>{{ message }}</li>
              {% endfor %}
            </ul>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ _('modal_close') }}</button>
          </div>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        $('#flashModal').modal('show');
      });
    </script>
  {% endif %}
{% endwith %}
{% if timestamps|length > 0 %}
  <div class="container">
    <div class="slider-container">
        <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card rounded-lg">
                <div class="card-body">
                    <div class="card-text">
                      <div id="app-list">
                        {% for app in unique_apps %}
                            <a href="{{ url_for('search', q="", app=app) }}"><span class="badge badge-pill badge-primary">{{ app }}</span></a>
                        {% endfor %}
                      </div>
                      <button id="toggle-apps" class="btn btn-link" data-expand-text="{{ _('expand') }}" data-collapse-text="{{ _('collapse') }}">{{ _('expand') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="slider-wrapper position-relative">
      <div id="timeNodes">
        {% for node in time_nodes %}
          <div class="time-node" data-timestamp="{{ node.timestamp }}">{{ node.desc }}</div>
        {% endfor %}
      </div>
      <div id="sliderPreview" class="slider-preview">
        <img id="previewImage" class="preview-img" src="" alt="Preview">
        <div id="previewTime" class="preview-time"></div>
      </div>
      <input type="range" class="slider custom-range" id="discreteSlider" min="0" max="{{timestamps|length - 1}}" step="1" value="{{timestamps|length - 1}}">
      <div class="slider-value" id="sliderValue">{{timestamps[0] | timestamp_to_human_readable }}</div>
    </div>
    </div>
    <div class="image-container" id="image-container">
      <img id="timestampImage" class="responsive-img no-lazy" src="/pictures/{{timestamps[0]}}.webp" alt="Image for timestamp">
    </div>
  </div>

  <!-- 存储时间戳数据 -->
  <div id="timestamps-data" data-timestamps="{{ timestamps|tojson }}" style="display: none;"></div>
{% else %}
  <div class="container">
      <div class="alert alert-info" role="alert">
          {{ _('empty_state') }}
      </div>
  </div>
{% endif %}
{% endblock %}

{% block page_scripts %}
<script src="{{ url_for('static', filename='scripts/timeline.js') }}"></script>
{% endblock %}
