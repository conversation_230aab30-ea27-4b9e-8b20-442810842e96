{% extends "template.html" %}
{% block content %}
    <style>
        body {
            padding-top: 40px;
            padding-bottom: 40px;
            background-color: #f5f5f5;
        }
        .error-container {
            max-width: 800px;
            padding: 15px;
            margin: 0 auto;
        }
        .error-heading {
            margin-bottom: 30px;
            border-bottom: 1px solid #e5e5e5;
            padding-bottom: 20px;
        }
        .error-details {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .error-traceback {
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .error-actions {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container error-container">
        <div class="error-heading text-center">
            <h1>{{ _('error_title') }}</h1>
            <p class="lead">{{ _('error_message') }}</p>
        </div>

        <div class="alert alert-danger">
            <h4>{{ error.type }}</h4>
            <p>{{ error.message }}</p>
        </div>

        {% if error.details %}
        <div class="error-details">
            <h4>{{ _('error_details') }}</h4>
            <ul>
                {% for key, value in error.details.items() %}
                <li><strong>{{ key }}:</strong> {{ value }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        {% if error.traceback %}
        <div class="error-traceback">
            <h4>{{ _('error_details') }}</h4>
            <pre>{{ error.traceback }}</pre>
        </div>
        {% endif %}

        <div class="error-actions text-center">
            <a href="/" class="btn btn-primary">{{ _('nav_home') }}</a>
            <button onclick="window.history.back()" class="btn btn-secondary">{{ _('modal_cancel') }}</button>
            <button onclick="location.reload()" class="btn btn-info">{{ _('error_retry') }}</button>
        </div>
    </div>
{% endblock %}
