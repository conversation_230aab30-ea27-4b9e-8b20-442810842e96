/**
 * MemoCoco 主样式表
 * 包含应用程序的主要样式定义
 */

/* 文本高亮和标签样式 */
.highlight {
    border: 2px solid red;
    position: absolute;
    pointer-events: none;
}

.text-label {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 2px;
    border: 1px solid #000;
    font-size: 12px;
    cursor: pointer;
}

/* 滑块容器样式 */
.slider-container {
    width: 100%;
    margin: 20px 0;
    position: relative;
}

/* 卡片样式 */
.card {
    position: relative;
    z-index: 10; /* 确保在时间节点上方 */
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 应用列表样式 */
#app-list {
    position: relative;
    z-index: 10;
    margin-bottom: 10px;
}

#app-list .badge {
    margin-right: 5px;
    margin-bottom: 5px;
    transition: background-color 0.2s;
}

#app-list .badge:hover {
    background-color: #0056b3;
}

/* 滑块包装器样式 */
.slider-wrapper {
    width: 100%;
    padding-top: 40px; /* 为时间节点留出空间 */
    margin-bottom: 20px;
}

/* 滑块样式 */
.slider {
    -webkit-appearance: none;
    width: 100%;
    height: 10px;
    border-radius: 5px;
    background: #e0e0e0;
    outline: none;
    opacity: 0.7;
    -webkit-transition: .2s;
    transition: opacity .2s;
}

.slider:hover {
    opacity: 1;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    -webkit-transition: .3s;
    transition: .3s;
}

.slider::-webkit-slider-thumb:hover {
    background: #45a049;
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border: 0;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    transition: .3s;
}

.slider::-moz-range-thumb:hover {
    background: #45a049;
}

.slider::-ms-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    transition: .3s;
}

.slider::-ms-thumb:hover {
    background: #45a049;
}

.slider-value {
    margin-top: 10px;
    font-size: 1.2em;
}

/* 滑块预览样式 */
.slider-preview {
    position: absolute;
    display: none;
    background-color: rgba(0, 0, 0, 0.9);
    border: 2px solid #4CAF50;
    border-radius: 12px;
    padding: 20px;
    z-index: 1000;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    transform: translate(-50%, 20px);
    margin-top: 10px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.slider-preview img {
    transform: scale(1.5);
    transform-origin: center center;
    max-width: none;
    max-height: 200px;
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
}

.slider-preview .preview-time {
    color: #ffffff;
    text-align: center;
    margin-top: 50px;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    display: block;
}

.preview-time {
    text-align: center;
    font-size: 12px;
    margin-top: 5px;
    color: #333;
}

/* 暗黑模式下的预览样式 */
.dark-mode .slider-preview {
    background-color: #333;
    border-color: #4CAF50;
}

.dark-mode .preview-time {
    color: #eee;
}

/* 时间节点样式 */
#timeNodes {
    position: absolute;
    width: 100%;
    height: 30px;
    top: 5px;
    left: 0;
    z-index: 5; /* 降低 z-index，确保在 app 列表下方 */
}

.time-node {
    position: absolute;
    background-color: #4CAF50;
    color: white;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    transform: translateX(-50%);
    white-space: nowrap;
    z-index: 5; /* 降低 z-index，确保在 app 列表下方 */
    cursor: pointer;
    transition: all 0.3s;
    top: 0;
    opacity: 1 !important; /* 确保始终可见 */
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.time-node:hover {
    background-color: #45a049;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    transform: translateX(-50%) translateY(-2px);
}

.time-node::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #4CAF50;
}

/* 图片容器样式 */
.image-container {
    margin-top: 20px;
    text-align: center;
    position: relative;
    display: inline-block;
}

.image-container img {
    max-width: 100%;
    height: auto;
}

/* 导航栏样式 */
.navbar .container {
    justify-content: center;
}

.form-inline {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 1000px; /* 可根据需要调整最大宽度 */
    margin: 0 auto;
}

.form-control {
    flex-grow: 1;
    margin-right: 10px;
}

/* 搜索结果样式 */
.search-result {
    margin-bottom: 20px;
}

.search-result-title {
    font-size: 1.2em;
    font-weight: bold;
}

.search-result-content {
    margin-top: 10px;
}

/* 分页样式 */
.pagination {
    justify-content: center;
    margin-top: 20px;
}

.pagination .page-item.active .page-link {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

.pagination .page-link {
    color: #4CAF50;
}

.pagination .page-link:hover {
    color: #45a049;
}

/* 模态框样式 */
.modal-xl {
    max-width: 90%;
}

.modal-content {
    border-radius: 0.5rem;
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
}

/* 主题过渡样式 */
.theme-transition,
.theme-transition * {
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out, border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out !important;
}

/* 暗黑模式样式 */
html.dark-mode,
html.dark-mode body {
    background-color: #121212;
    color: #f8f9fa;
}

body.dark-mode,
html.dark-mode body {
    background-color: #121212;
    color: #f8f9fa;
}

body.dark-mode .slider {
    background: #444;
}

body.dark-mode .slider::-webkit-slider-thumb {
    background: #6c757d;
}

body.dark-mode .slider::-webkit-slider-thumb:hover {
    background: #5a6268;
}

body.dark-mode .slider::-moz-range-thumb {
    background: #6c757d;
}

body.dark-mode .slider::-moz-range-thumb:hover {
    background: #5a6268;
}

body.dark-mode .time-node {
    background-color: #6c757d;
}

body.dark-mode .time-node:hover {
    background-color: #5a6268;
}

body.dark-mode .time-node::after {
    border-top-color: #6c757d;
}

body.dark-mode .text-label {
    background-color: rgba(50, 50, 50, 0.7);
    color: #fff;
    border-color: #444;
}

/* 暗黑模式下的列表组样式 */
body.dark-mode .list-group-item {
    background-color: #343a40;
    color: #f8f9fa;
    border-color: #495057;
}

body.dark-mode .badge-secondary {
    background-color: #6c757d;
}

body.dark-mode .badge-info {
    background-color: #17a2b8;
}

body.dark-mode .text-muted {
    color: #adb5bd !important;
}

/* 暗黑模式下的卡片样式 */
body.dark-mode .card {
    background-color: #343a40;
    border-color: #495057;
}

body.dark-mode .card-body {
    background-color: #343a40;
}

body.dark-mode .card-footer {
    background-color: #2c3136;
    border-top-color: #495057;
}

/* 暗黑模式下的下拉菜单样式 */
body.dark-mode .dropdown-menu,
html.dark-mode .dropdown-menu {
    background-color: #343a40;
    border-color: #495057;
}

body.dark-mode .dropdown-item,
html.dark-mode .dropdown-item {
    color: #f8f9fa;
}

body.dark-mode .dropdown-item:hover,
html.dark-mode .dropdown-item:hover {
    background-color: #495057;
    color: #ffffff;
}

body.dark-mode .dropdown-item.active,
html.dark-mode .dropdown-item.active {
    background-color: #007bff;
    color: #ffffff;
}

body.dark-mode .btn-outline-secondary,
html.dark-mode .btn-outline-secondary {
    color: #adb5bd;
    border-color: #6c757d;
}

body.dark-mode .btn-outline-secondary:hover,
html.dark-mode .btn-outline-secondary:hover {
    background-color: #6c757d;
    color: #ffffff;
}

/* 辅助类 */
.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

.text-center {
    text-align: center;
}

.d-flex {
    display: flex;
}

.justify-content-center {
    justify-content: center;
}

.align-items-center {
    align-items: center;
}

.flex-wrap {
    flex-wrap: wrap;
}

.w-100 {
    width: 100%;
}

.h-100 {
    height: 100%;
}

.position-relative {
    position: relative;
}

.overflow-hidden {
    overflow: hidden;
}

.rounded {
    border-radius: 0.25rem;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.transition {
    transition: all 0.3s ease;
}