/*
 * MemoCoco 响应式设计样式
 * 提供自适应内容展示和暗黑模式支持
 */

/* 基础响应式设置 */
html {
  font-size: 16px;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  transition: background-color 0.3s, color 0.3s;
}

/* 容器响应式调整 */
.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

/* 图片响应式处理 */
.responsive-img {
  max-width: 100%;
  height: auto;
  display: block;
}

.lazy-load {
  opacity: 0;
  transition: opacity 0.3s;
}

.lazy-load.loaded {
  opacity: 1;
}

/* 响应式卡片布局 */
.card {
  transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式导航栏 */
.navbar {
  padding: 0.5rem 1rem;
}

.navbar-brand {
  display: flex;
  align-items: center;
}

/* 响应式表单元素 */
.form-control {
  font-size: 1rem;
  line-height: 1.5;
  padding: 0.375rem 0.75rem;
}

/* 响应式按钮 */
.btn {
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  transition: all 0.2s;
}

/* 响应式模态框 */
.modal-content {
  border-radius: 0.5rem;
}

/* 响应式时间轴 */
.slider-container {
  width: 100%;
  padding: 0 10px;
}

.time-node {
  font-size: 0.75rem;
}

/* 响应式搜索结果 */
.search-result {
  margin-bottom: 1.5rem;
}

/* 暗黑模式样式 */
body.dark-mode {
  background-color: #121212;
  color: #e0e0e0;
}

body.dark-mode .navbar {
  background-color: #1e1e1e !important;
}

body.dark-mode .card {
  background-color: #1e1e1e;
  border-color: #333;
}

body.dark-mode .card-footer {
  background-color: #252525 !important;
  border-color: #333;
}

body.dark-mode .form-control {
  background-color: #2a2a2a;
  border-color: #444;
  color: #e0e0e0;
}

body.dark-mode .btn-outline-primary {
  color: #81b4ff;
  border-color: #81b4ff;
}

body.dark-mode .btn-outline-primary:hover {
  background-color: #81b4ff;
  color: #121212;
}

body.dark-mode .modal-content {
  background-color: #1e1e1e;
  border-color: #333;
}

body.dark-mode .time-node {
  background-color: #3a3a3a;
  color: #e0e0e0;
}

body.dark-mode .time-node::after {
  border-top-color: #3a3a3a;
}

body.dark-mode .slider {
  background-color: #444;
}

body.dark-mode .badge-primary {
  background-color: #4a6da7;
}

body.dark-mode .text-muted {
  color: #aaa !important;
}

/* 媒体查询 - 小屏幕设备 */
@media (max-width: 576px) {
  html {
    font-size: 14px;
  }

  .container {
    padding-right: 10px;
    padding-left: 10px;
  }

  .col-md-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .navbar .form-inline {
    flex-direction: column;
  }

  .navbar .form-control {
    margin-bottom: 0.5rem;
    width: 100%;
  }

  .time-node {
    font-size: 0.7rem;
    padding: 1px 3px;
  }

  .slider-container {
    padding: 0 5px;
  }

  .modal-dialog {
    margin: 0.5rem;
  }
}

/* 媒体查询 - 中等屏幕设备 */
@media (min-width: 577px) and (max-width: 768px) {
  .col-md-3 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .time-node {
    font-size: 0.7rem;
  }
}

/* 媒体查询 - 大屏幕设备 */
@media (min-width: 769px) and (max-width: 992px) {
  .col-md-3 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
}

/* 媒体查询 - 超大屏幕设备 */
@media (min-width: 993px) {
  .container {
    max-width: 1200px;
  }
}

/* 主题切换按钮 */
.theme-switcher-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

#theme-toggle {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  background-color: #fff;
  border-color: #ddd;
}

body.dark-mode #theme-toggle {
  background-color: #333;
  border-color: #555;
  color: #fff;
}

#theme-toggle:hover {
  transform: scale(1.1);
}

#theme-toggle:active {
  transform: scale(0.95);
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background-color: white !important;
    color: black !important;
  }

  .container {
    width: 100%;
    max-width: none;
  }

  .card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
  }

  .theme-switcher-container {
    display: none !important;
  }
}
